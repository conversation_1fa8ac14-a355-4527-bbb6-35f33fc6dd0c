#!/usr/bin/env python3
"""
Magnetic-One Test Script with Debug Logging
==========================================

This script extends the original run_m1_test.py with debug-level logging
that saves detailed execution traces to log/generated/debug/ directory.

Usage:
    python run_m1_debug.py --scenario 1 --endpoint_type cloudgpt --model gpt-4o-mini-20240718
    
Environment:
    conda activate m1
"""

import os
import json
import argparse
import asyncio
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Union

# -----------------------------------------------------------
# AutoGen / Magnetic-One Imports
# -----------------------------------------------------------
from autogen_ext.models.openai import OpenAIChatCompletionClient, AzureOpenAIChatCompletionClient
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_core.models import ChatCompletionClient
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# Import our utility functions for LLM call logging and debug logging
from utils import setup_llm_logging, finalize_llm_logging, setup_debug_logging, finalize_debug_logging, log_debug_message

# -----------------------------------------------------------
# Configuration
# -----------------------------------------------------------
# OpenAI Configuration
API_KEY = os.getenv("OPENAI_API_KEY")
API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")

# Ollama Configuration
OLLAMA_HOST = "http://localhost:11434"  # Default Ollama host

# -----------------------------------------------------------
# Tool Functions
# -----------------------------------------------------------
SCRIPT_DIR = Path(__file__).parent.absolute()
DATA_DIR = SCRIPT_DIR.parent / "Agents_Failure_Attribution/Who&When/Hand-Crafted"
OUT_DIR = SCRIPT_DIR.parent / "logs/generated"


def load_scenario(scenario_id: str) -> dict[str, Any]:
    """Load scenario JSON file and return as dictionary."""
    path = DATA_DIR / f"{scenario_id}.json"
    if not path.exists():
        raise FileNotFoundError(f"Scenario file not found: {path}")
    with path.open(encoding="utf-8") as f:
        return json.load(f)


def save_final_trace(scenario_id: str, endpoint_type: str, model: str, trace: dict[str, Any], run_timestamp: str = None) -> None:
    """Save final trace to ../logs/generated/scenario_{id}_{endpoint_type}_{model}_{timestamp}.json"""
    OUT_DIR.mkdir(parents=True, exist_ok=True)

    # 构建带时间戳的文件名
    if run_timestamp:
        out_path = OUT_DIR / f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}.json"
    else:
        out_path = OUT_DIR / f"scenario_{scenario_id}_{endpoint_type}_{model}.json"

    with out_path.open("w", encoding="utf-8") as f:
        json.dump(trace, f, indent=2, ensure_ascii=False)
    print(f"✓ Final trace saved to {out_path}")
    

def save_incremental_log(scenario_id: str, endpoint_type: str, model: str, step: int, role: str, content: str, message_time: datetime = None, run_timestamp: str = None) -> None:
    """Append complete step to incremental log file"""
    OUT_DIR.mkdir(parents=True, exist_ok=True)

    # 构建带时间戳的文件名
    if run_timestamp:
        log_path = OUT_DIR / f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}.log"
    else:
        log_path = OUT_DIR / f"scenario_{scenario_id}_{endpoint_type}_{model}.log"

    # 使用传入的消息时间或当前时间
    if message_time:
        # 如果有时区信息，转换为本地时间显示
        if message_time.tzinfo:
            local_time = message_time.astimezone()
            timestamp = local_time.strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
            timestamp += f" (UTC: {message_time.strftime('%H:%M:%S')})"
        else:
            timestamp = message_time.strftime("%H:%M:%S.%f")[:-3]
    else:
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒

    # 保存完整内容，使用分隔符便于阅读
    separator = "=" * 80
    log_entry = f"\n{separator}\n[{timestamp}] Step {step} - Role: {role}\n{separator}\n{content}\n{separator}\n\n"

    with log_path.open("a", encoding="utf-8") as f:
        f.write(log_entry)


def get_client(endpoint_type: str = "ollama", model: str = None) -> ChatCompletionClient:
    """Get appropriate model client based on configuration."""
    if endpoint_type == "ollama":
        return OllamaChatCompletionClient(
            model=model,
            host=OLLAMA_HOST,
        )
    elif endpoint_type == "openai":
        if not API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable must be set for OpenAI usage")
        return OpenAIChatCompletionClient(
            model=model,
            api_key=API_KEY,
            base_url=API_BASE,
        )
    elif endpoint_type == "cloudgpt":
        assert model is not None, "Model name must be specified for cloudgpt"
        return AzureOpenAIChatCompletionClient(
            model=model,
            # api_key=API_KEY,
            # base_url=API_BASE,
        )
    else:
        raise ValueError(f"Unknown endpoint type: {endpoint_type}")


# -----------------------------------------------------------
# Core: Run Magnetic-One with Debug Logging
# -----------------------------------------------------------
async def run_m1_with_debug(query: str, endpoint_type: str = "ollama", model: str = None, scenario_id: str = None, run_timestamp: str = None, debug_log_path=None) -> tuple[list[dict[str, str]], dict[str, str]]:
    """Run Magnetic-One system with debug logging and capture complete message history."""
    start_time = datetime.now(timezone.utc)

    # 如果没有提供运行时间戳，则生成一个（UTC时间）
    if run_timestamp is None:
        utc_time = start_time
        run_timestamp = f"{utc_time.year:04d}{utc_time.month:02d}{utc_time.day:02d}_{utc_time.hour:02d}{utc_time.minute:02d}{utc_time.second:02d}UTC"

    # Start debug logging if debug log path is provided
    if debug_log_path:
        log_debug_message(debug_log_path, f"Starting Magnetic-One execution with {endpoint_type} model {model}")

    try:
        client = get_client(endpoint_type, model)

        # 创建代码执行器
        code_executor = LocalCommandLineCodeExecutor()

        # 创建 MagenticOne 实例，包含代码执行器
        m1 = MagenticOne(client=client, code_executor=code_executor)

        # 初始化history，包含步数和时间戳
        history: list[dict[str, str]] = [{
            "step": 0,
            "role": "human",
            "content": query,
            "timestamp": start_time.isoformat()
        }]

        # 初始化log文件并保存初始查询
        if scenario_id:
            # 构建带时间戳的log文件路径
            log_path = OUT_DIR / f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}.log"
            OUT_DIR.mkdir(parents=True, exist_ok=True)

            start_msg = f"=== Started scenario {scenario_id} with {endpoint_type} at {start_time.strftime('%Y-%m-%d %H:%M:%S')} UTC ===\n"
            start_msg += f"=== Run timestamp: {run_timestamp} ===\n"
            start_msg += f"=== Using enhanced timestamp extraction from message created_at ===\n"
            start_msg += f"=== DEBUG LOGGING ENABLED ===\n"
            log_path.write_text(start_msg, encoding="utf-8")

            # 保存初始人类查询
            save_incremental_log(scenario_id, endpoint_type, model, 0, "human", query, start_time, run_timestamp)
            print(f"✓ Started logging to {log_path}")
            print(f"✓ Run timestamp: {run_timestamp}")

        # Debug log initial query
        if debug_log_path:
            log_debug_message(debug_log_path, f"Step 0 [human]: {query[:100]}...")

        step_count = 0
        async for item in m1.run_stream(task=query):
            if not hasattr(item, "content"):
                continue

            role = getattr(item, "role", None) or getattr(item, "source", "") or "assistant"
            content_str = item.content if isinstance(item.content, str) else str(item.content)

            # 跳过重复的用户输入（避免与step 0的human重复）
            if role in ["user", "human"] and content_str.strip() == query.strip():
                continue

            step_count += 1
            # 优先使用 item 的 created_at 时间戳，如果没有则使用当前UTC时间
            current_time = getattr(item, 'created_at', datetime.now(timezone.utc))

            # Terminal preview
            preview = content_str[:120].replace('\n', ' ')
            print(f"[{role}] {preview}...")

            # 添加到history，包含步数和时间戳
            history.append({
                "step": step_count,
                "role": role,
                "content": content_str,
                "timestamp": current_time.isoformat()
            })

            # 实时保存到log文件
            if scenario_id:
                save_incremental_log(scenario_id, endpoint_type, model, step_count, role, content_str, current_time, run_timestamp)

            # Debug logging for each step
            if debug_log_path:
                content_preview = content_str[:100] + "..." if len(content_str) > 100 else content_str
                log_debug_message(debug_log_path, f"Step {step_count} [{role}]: {content_preview}")
                log_debug_message(debug_log_path, f"Agent[{role}] response: {content_preview}")

    except Exception as e:
        if debug_log_path:
            log_debug_message(debug_log_path, f"ERROR[run_m1_with_debug]: {type(e).__name__}: {str(e)}")
        raise
    finally:
        # Debug logging completed
        if debug_log_path:
            log_debug_message(debug_log_path, "Execution completed")

    end_time = datetime.now(timezone.utc)
    duration = end_time - start_time

    # 计算时间信息
    timing_info = {
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "duration_seconds": duration.total_seconds(),
        "duration_formatted": str(duration).split('.')[0]  # 去掉微秒，格式如 "0:02:35"
    }

    if scenario_id:
        # 在log文件末尾添加完成信息
        log_path = OUT_DIR / f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}.log"
        with log_path.open("a", encoding="utf-8") as f:
            f.write(f"=== Completed at {end_time.strftime('%Y-%m-%d %H:%M:%S')} UTC | Duration: {timing_info['duration_formatted']} | Steps: {step_count} ===\n")
        print(f"✓ Logging completed. Total steps: {step_count}, Duration: {timing_info['duration_formatted']}")

    return history, timing_info


# -----------------------------------------------------------
# CLI Main Entry Point
# -----------------------------------------------------------
async def main_async() -> None:
    """Main async function for CLI execution with debug logging."""
    parser = argparse.ArgumentParser(description="Magnetic-One Test with Debug Logging")
    parser.add_argument("--scenario", required=True, help="Scenario ID (e.g. 1)")
    parser.add_argument("--endpoint_type", choices=["ollama", "openai", "cloudgpt"], default="cloudgpt",
                      help="Endpoint type to use (default: cloudgpt)")
    parser.add_argument("--model", default='gpt-4o-mini-20240718', help="Model name (default: gpt-4o-mini-20240718)")
    args = parser.parse_args()

    # Load scenario
    scenario = load_scenario(args.scenario)
    query = scenario["question"]

    # 生成运行时间戳（UTC时间）
    run_start_time = datetime.now(timezone.utc)
    run_timestamp = f"{run_start_time.year:04d}{run_start_time.month:02d}{run_start_time.day:02d}_{run_start_time.hour:02d}{run_start_time.minute:02d}{run_start_time.second:02d}UTC"

    # Setup debug logging
    debug_log_path = setup_debug_logging(args.scenario, args.endpoint_type, args.model, OUT_DIR, run_timestamp)

    # Setup LLM logging before any LLM calls
    llm_log_path = setup_llm_logging(args.scenario, args.endpoint_type, args.model, OUT_DIR, run_timestamp)

    print(f"\n▶ Running Magnetic-One test with DEBUG LOGGING using {args.endpoint_type}")
    print(f"▶ Model: {args.model}")
    print(f"▶ Run timestamp: {run_timestamp}")
    print(f"▶ Query: {query}")
    print(f"▶ Debug log will be saved to: {debug_log_path}")
    print()

    # Execute with debug logging
    history, timing_info = await run_m1_with_debug(query, args.endpoint_type, args.model, args.scenario, run_timestamp, debug_log_path)

    # Save final JSON result with timing information
    final_trace = {
        "history": history,
        "question": query,
        "ground_truth": scenario.get("ground_truth", ""),
        "question_ID": args.scenario,
        "execution_type": f"m1_debug_{args.endpoint_type}_{args.model}",
        "model": args.model,
        "run_timestamp": run_timestamp,
        "total_steps": len(history) - 1,  # -1 for initial human message
        "debug_enabled": True,
        **timing_info  # 包含 start_time, end_time, duration_seconds, duration_formatted
    }

    save_final_trace(args.scenario, args.endpoint_type, args.model, final_trace, run_timestamp)

    # Finalize LLM logging with summary
    finalize_llm_logging(llm_log_path, len(history) - 1, timing_info['duration_formatted'])

    # Finalize debug logging
    finalize_debug_logging(debug_log_path, len(history) - 1, timing_info['duration_formatted'])

    print(f"✓ Execution completed in {timing_info['duration_formatted']}")
    print(f"✓ Steps: {len(history) - 1}")
    print(f"✓ Log: scenario_{args.scenario}_{args.endpoint_type}_{args.model}_{run_timestamp}.log")
    print(f"✓ JSON: scenario_{args.scenario}_{args.endpoint_type}_{args.model}_{run_timestamp}.json")
    print(f"✓ LLM calls log: scenario_{args.scenario}_{args.endpoint_type}_{args.model}_{run_timestamp}_llm_calls.log")
    print(f"✓ Debug log: logs/generated/debug/scenario_{args.scenario}_{args.endpoint_type}_{args.model}_{run_timestamp}_debug.log")
    print(f"✓ Debug JSON: logs/generated/debug/scenario_{args.scenario}_{args.endpoint_type}_{args.model}_{run_timestamp}_debug.json")
    print(f"✓ Run timestamp: {run_timestamp}")


if __name__ == "__main__":
    asyncio.run(main_async())
